
import React, { useState } from 'react';
import { Sparkles, List, Zap, BookOpen } from 'lucide-react';
import NewsLayout from '../components/NewsLayout';
import NewsWidget from '../components/NewsWidget';

const Index = () => {
  const [widgetMode, setWidgetMode] = useState<'summary' | 'related' | null>(null);

  return (
    <div className="relative">
      <NewsLayout />
      
      {/* Control Icons - only show when no widget is open */}
      {!widgetMode && (
        <div className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-50 flex flex-col gap-3 sm:gap-4">
          {/* AI Summary Button */}
          <div className="relative group">
            <button
              onClick={() => setWidgetMode('summary')}
              className="relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                         bg-gradient-to-br from-[#6EE35E] to-[#6EE35E] text-white
                         pulse-glow float-animation hover:shadow-[0_0_40px_rgba(110,227,94,0.6)]
                         active:scale-95 group-hover:rotate-12 touch-target mobile-button"
              title="AI Summary"
            >
              <Zap size={24} className="sm:w-7 sm:h-7 drop-shadow-lg" />
              <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </button>

            {/* Tooltip */}
            <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100
                            transition-all duration-300 pointer-events-none hidden sm:block">
              <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                              shadow-lg border border-gray-700">
                AI Summary
                <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
              </div>
            </div>
          </div>

          {/* Related Posts Button */}
          <div className="relative group">
            <button
              onClick={() => setWidgetMode('related')}
              className="relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                         bg-gradient-to-br from-[#4C4C4C] to-[#4C4C4C] text-white
                         pulse-glow float-animation hover:shadow-[0_0_40px_rgba(76,76,76,0.6)]
                         active:scale-95 group-hover:rotate-12 touch-target mobile-button"
              title="Related Posts"
              style={{ animationDelay: '1s' }}
            >
              <BookOpen size={24} className="sm:w-7 sm:h-7 drop-shadow-lg" />
              <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </button>

            {/* Tooltip */}
            <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100
                            transition-all duration-300 pointer-events-none hidden sm:block">
              <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                              shadow-lg border border-gray-700">
                Related Posts
                <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <NewsWidget mode={widgetMode} onClose={() => setWidgetMode(null)} />
    </div>
  );
};

export default Index;
