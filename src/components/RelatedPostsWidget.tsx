
import React, { useState } from 'react';
import { BookOpen } from 'lucide-react';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface RelatedPostsWidgetProps {
  relatedPosts: RelatedPost[];
  isExpanded: boolean;
  onToggle: () => void;
}

const RelatedPostsWidget: React.FC<RelatedPostsWidgetProps> = ({
  relatedPosts,
  isExpanded,
  onToggle
}) => {


  return (
    <div className="space-y-6">

      {/* Posts Grid */}
      <div className="space-y-4">
        {relatedPosts.map((post, index) => (
          <div
            key={index}
            className="group bg-white border border-gray-200/50 rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 interactive-hover"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Post Header */}
            <div className="p-6 bg-gradient-to-r from-gray-50 to-white">
              <div className="mb-4">
                <h4 className="font-bold text-[#4C4C4C] text-lg leading-tight group-hover:text-[#6EE35E] transition-colors">
                  {post.post_title}
                </h4>
              </div>

              {/* Relevance Summary */}
              <div className="bg-white/80 rounded-xl p-4 border border-gray-100">
                <p className="text-gray-700 leading-relaxed font-medium">
                  {post.relevance_summary}
                </p>
              </div>

            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RelatedPostsWidget;
