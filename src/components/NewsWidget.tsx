
import React, { useState, useEffect } from 'react';
import { X, Zap, BookOpen } from 'lucide-react';
import NewsSummaryWidget from './NewsSummaryWidget';
import RelatedPostsWidget from './RelatedPostsWidget';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface NewsData {
  post_id: string;
  main_post_summary: string;
  related_posts: RelatedPost[];
}

interface NewsWidgetProps {
  mode: 'summary' | 'related' | null;
  onClose: () => void;
}

const NewsWidget: React.FC<NewsWidgetProps> = ({ mode, onClose }) => {
  const [newsData, setNewsData] = useState<NewsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const contentRef = React.useRef<HTMLDivElement>(null);

  // Calculate optimal widget size based on content
  const getOptimalWidth = () => {
    // Fixed 600px width, responsive on smaller screens
    return 'min(95vw, 600px)';
  };

  const getOptimalHeight = () => {
    return 'min(800px, 90vh)'; // Fixed height as requested
  };

  // Mock API call - in real implementation, this would fetch from your backend
  const fetchNewsData = async () => {
    setLoading(true);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockData: NewsData = {
      post_id: "2334",
      main_post_summary: "• ई–सेवा, फोन पे र नेपाल इन्भेष्टमेन्ट बैंकबीच अनलाइन भुक्तानी सम्बन्धी सम्झौता भएको छ जसअन्तर्गत बैंकका ग्राहकहरूले ई–सेवामा रकम लोड गरी भुक्तानी सेवा उपयोग गर्न सक्नेछन्।\n• ग्राहकहरूले मोबाइल रिचार्जदेखि लिएर बिजुली, खानेपानी, यातायात टिकट तथा विद्यालय शुल्क भुक्तानीसम्म विभिन्न सुविधाहरू ई–सेवाबाट सहजै गर्न सक्नेछन्।\n• बैंकको इन्टरनेट बैंकिङ वा शाखाबाट पैसा ई–सेवामा लोड गर्न सकिने र ई–सेवामा भएको रकम बैंकमा पनि जम्मा गर्न सकिने व्यवस्था भएको छ।",
      related_posts: [
        {
          post_title: "ई–सेवा उत्सव उपहार विजेता सार्वजनिक",
          relevance_summary: "दुवै समाचारमा ई–सेवामा धेरै मर्चेन्टहरूको सहभागिता भएको उल्लेख छ। पहिलो समाचारमा नेपाल इन्भेष्टमेन्ट बैंक र फोन पे सँगको नयाँ सम्झौताको चर्चा छ।",
          qa_pairs: [
            {
              question: "नेपाल इन्भेष्टमेन्ट बैंक र ई–सेवाबीच भएको सम्झौताले ग्राहकहरूलाई के कस्ता भुक्तानी सेवाहरू उपलब्ध गराउँछ?",
              answer: "नेपाल इन्भेष्टमेन्ट बैंक र ई–सेवाबीच भएको सम्झौताअनुसार ग्राहकहरूले मोबाइल रिचार्ज, बिजुली, खानेपानी, यातायात टिकट, विद्यालय शुल्क लगायत विभिन्न भुक्तानी सेवा ई–सेवाबाट सहजै गर्न सक्नेछन्।"
            },
            {
              question: "ई–सेवामा हाल कति बैंक तथा वित्तीय संस्था आबद्ध छन्?",
              answer: "ई–सेवामा हाल ५६ वटा बैंक तथा वित्तीय संस्था आबद्ध छन्। यी संस्थाहरूबाट ग्राहकहरूले ई–सेवामा सजिलै रकम जम्मा गर्न र ई–सेवाबाट बैंक खातामा रकम पठाउन सक्ने सुविधा पाउँछन्।"
            }
          ],
          score: 0.561,
          post_url: "https://www.techpana.com/4891/4891"
        },
        {
          post_title: "ई-सेवा जस्तै ई-ढेवाको सेवा, डिजिटल वालेटकै ब्यालेन्समा ७ प्रतिश ब्याज",
          relevance_summary: "दुवै समाचारले ग्राहकलाई सहज र मूल्यवान सेवा दिनुपर्ने आवश्यकतालाई जोड दिएका छन्। मुख्य समाचारले सेवाका प्राविधिक सुविधाहरू र उपयोगबारे विस्तृत जानकारी दिएको छ।",
          qa_pairs: [
            {
              question: "फोन पे र नेपाल इन्भेष्टमेन्ट बैंकको सहकार्यले कस्तो प्रविधिको प्रयोग गरेर भुक्तानीलाई प्रवद्र्धन गरिरहेको छ?",
              answer: "फोन पे र नेपाल इन्भेष्टमेन्ट बैंकले क्युआर कोड प्रविधिको प्रयोग गरी भुक्तानीलाई प्रवद्र्धन गरिरहेका छन्। यसमा क्यासब्याक र छुट सुविधा पनि उपलब्ध गराइएको छ।"
            }
          ],
          score: 0.545,
          post_url: "https://www.techpana.com/9306/9306"
        }
      ]
    };
    
    setNewsData(mockData);
    setLoading(false);
  };

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 300);
  };



  useEffect(() => {
    if (mode && !newsData) {
      fetchNewsData();
    }
  }, [mode, newsData]);

  if (!mode) return null;

  return (
    <>
      {/* Widget Container */}
      <div
        className={`fixed bottom-4 right-4 z-[9999]
                   bg-white shadow-2xl rounded-xl sm:rounded-2xl border border-gray-200/50
                   transform transition-all duration-500 ease-out
                   ${mode && !isClosing
                     ? 'translate-x-0 opacity-100 scale-100'
                     : 'translate-x-full opacity-0 scale-95'
                   }
                   content-responsive mobile-optimized floating-widget ${mode === 'summary' ? 'widget-summary' : 'widget-related'}`}
        style={{
          width: getOptimalWidth(),
          height: getOptimalHeight(),
          maxHeight: 'calc(100vh - 2rem)',
          minHeight: '300px',
          background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255,255,255,0.5)'
        }}
      >
        {/* Header */}
        <div className={`relative overflow-hidden transition-all duration-300 ${
          mode === 'summary'
            ? 'bg-gradient-to-r from-[#6EE35E] to-[#6EE35E]'
            : 'bg-gradient-to-r from-[#4C4C4C] to-[#4C4C4C]'
        }`}>
          {/* Animated background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0 animate-pulse" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>

          <div className="relative p-4 sm:p-6 flex justify-between items-center mobile-header">
            <div className="flex items-center gap-3 sm:gap-4">
              <div className="p-2 bg-white/20 rounded-xl">
                {mode === 'summary' ? (
                  <Zap className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                ) : (
                  <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                )}
              </div>
              <div>
                <h3 className="font-bold text-lg sm:text-xl text-white drop-shadow-sm">
                  {mode === 'summary' ? 'AI Post Summary' : 'Related Posts'}
                </h3>
                <p className="text-white/80 text-xs sm:text-sm">
                  {mode === 'summary' ? 'Powered by AI' : `${newsData?.related_posts?.length || 0} posts found`}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-1 sm:gap-2">
              <button
                onClick={handleClose}
                className="p-2 hover:bg-white/20 rounded-xl transition-all duration-200 text-white/80 hover:text-white hover:rotate-90 touch-target mobile-button"
                title="Close"
              >
                <X size={18} className="sm:w-5 sm:h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className="flex-1 overflow-hidden"
        >
          <div className="h-full smart-scroll mobile-content">
            {loading ? (
              <div className="p-6 sm:p-8 lg:p-12 text-center">
                <div className="relative mx-auto mb-4 sm:mb-6 w-12 h-12 sm:w-16 sm:h-16">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                  <div className={`absolute inset-0 rounded-full border-4 border-t-transparent animate-spin ${
                    mode === 'summary' ? 'border-[#6EE35E]' : 'border-[#4C4C4C]'
                  }`}></div>
                </div>
                <p className="text-[#4C4C4C] text-base sm:text-lg font-medium">Loading content...</p>
                <p className="text-gray-500 text-sm mt-2">Please wait while we fetch the data</p>
              </div>
            ) : (
              <div className="p-4 sm:p-6 fade-in-up mobile-optimized adaptive-content">
                {newsData && mode === 'summary' && (
                  <NewsSummaryWidget
                    summary={newsData.main_post_summary}
                    postId={newsData.post_id}
                    isExpanded={true}
                    onToggle={() => {}}
                  />
                )}

                {newsData && mode === 'related' && (
                  <RelatedPostsWidget
                    relatedPosts={newsData.related_posts}
                    isExpanded={true}
                    onToggle={() => {}}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default NewsWidget;
