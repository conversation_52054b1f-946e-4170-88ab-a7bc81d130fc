
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { ChevronUp, ChevronDown, ExternalLink } from 'lucide-react';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface RelatedPostsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  relatedPosts: RelatedPost[];
}

const RelatedPostsDialog: React.FC<RelatedPostsDialogProps> = ({
  isOpen,
  onClose,
  relatedPosts
}) => {
  const [expandedPost, setExpandedPost] = useState<number | null>(null);
  const [expandedQA, setExpandedQA] = useState<string | null>(null);

  const togglePost = (index: number) => {
    setExpandedPost(expandedPost === index ? null : index);
  };

  const toggleQA = (question: string) => {
    setExpandedQA(expandedQA === question ? null : question);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-[#4C4C4C] text-xl font-bold">
            Related Posts ({relatedPosts.length})
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {relatedPosts.map((post, index) => (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => togglePost(index)}
                className="w-full p-4 text-left hover:bg-gray-50 transition-colors flex justify-between items-start gap-3"
              >
                <div className="flex-1">
                  <h5 className="font-medium text-[#4C4C4C] mb-2 line-clamp-2">
                    {post.post_title}
                  </h5>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span className="bg-[#6EE35E] text-white px-2 py-1 rounded">
                      {Math.round(post.score * 100)}% match
                    </span>
                    <span>{post.qa_pairs.length} Q&As</span>
                  </div>
                </div>
                {expandedPost === index ? (
                  <ChevronUp size={20} className="text-gray-400 flex-shrink-0" />
                ) : (
                  <ChevronDown size={20} className="text-gray-400 flex-shrink-0" />
                )}
              </button>

              {expandedPost === index && (
                <div className="border-t border-gray-200">
                  {/* Relevance Summary */}
                  <div className="p-4 bg-gray-50">
                    <h6 className="font-medium text-[#4C4C4C] mb-2">Relevance</h6>
                    <p className="text-sm text-gray-600">{post.relevance_summary}</p>
                  </div>

                  {/* Q&A Pairs */}
                  <div className="p-4">
                    <h6 className="font-medium text-[#4C4C4C] mb-3">Questions & Answers</h6>
                    <div className="space-y-2">
                      {post.qa_pairs.map((qa, qaIndex) => (
                        <div key={qaIndex} className="border border-gray-100 rounded">
                          <button
                            onClick={() => toggleQA(`${index}-${qaIndex}`)}
                            className="w-full p-3 text-left hover:bg-gray-50 transition-colors"
                          >
                            <div className="flex justify-between items-start gap-2">
                              <span className="text-sm font-medium text-[#4C4C4C] flex-1">
                                {qa.question}
                              </span>
                              {expandedQA === `${index}-${qaIndex}` ? (
                                <ChevronUp size={16} className="text-gray-400 flex-shrink-0" />
                              ) : (
                                <ChevronDown size={16} className="text-gray-400 flex-shrink-0" />
                              )}
                            </div>
                          </button>
                          {expandedQA === `${index}-${qaIndex}` && (
                            <div className="px-3 pb-3 text-sm text-gray-600 border-t border-gray-100 pt-3">
                              {qa.answer}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* View Full Post Button */}
                  <div className="p-4 border-t border-gray-200">
                    <button
                      onClick={() => window.open(post.post_url, '_blank')}
                      className="w-full bg-[#6EE35E] hover:bg-[#5dd24d] text-white py-2 px-4 rounded transition-colors flex items-center justify-center gap-2 text-sm font-medium"
                    >
                      <ExternalLink size={14} />
                      View Full Post
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RelatedPostsDialog;
