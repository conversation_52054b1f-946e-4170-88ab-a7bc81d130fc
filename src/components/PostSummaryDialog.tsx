
import React from 'react';
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { ExternalLink } from 'lucide-react';

interface PostSummaryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  summary: string;
  postId: string;
}

const PostSummaryDialog: React.FC<PostSummaryDialogProps> = ({
  isOpen,
  onClose,
  summary,
  postId
}) => {
  const formatSummary = (summary: string) => {
    return summary.split('\n').map((line, index) => (
      <div key={index} className="mb-2 last:mb-0">
        {line.trim()}
      </div>
    ));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-[#4C4C4C] text-xl font-bold">
            Post Summary
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-700 leading-relaxed">
              {formatSummary(summary)}
            </div>
          </div>
          
          <button
            onClick={() => window.open('#', '_blank')}
            className="w-full bg-[#6EE35E] hover:bg-[#5dd24d] text-white py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2 font-medium"
          >
            <ExternalLink size={18} />
            View Full Post
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PostSummaryDialog;
