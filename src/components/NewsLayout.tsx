
import React from 'react';
import { <PERSON>, <PERSON>u, Bell, User } from 'lucide-react';

const NewsLayout: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-[#6EE35E] rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">TP</span>
                </div>
                <span className="text-xl font-bold text-[#4C4C4C]">TechPana</span>
              </div>
              <nav className="hidden md:flex space-x-8">
                <a href="#" className="text-[#4C4C4C] hover:text-[#6EE35E] font-medium">Home</a>
                <a href="#" className="text-[#4C4C4C] hover:text-[#6EE35E] font-medium">Technology</a>
                <a href="#" className="text-[#4C4C4C] hover:text-[#6EE35E] font-medium">Business</a>
                <a href="#" className="text-[#4C4C4C] hover:text-[#6EE35E] font-medium">Economy</a>
                <a href="#" className="text-[#4C4C4C] hover:text-[#6EE35E] font-medium">Sports</a>
              </nav>
            </div>
            <div className="flex items-center gap-4">
              <Search className="w-5 h-5 text-gray-400 cursor-pointer hover:text-[#6EE35E]" />
              <Bell className="w-5 h-5 text-gray-400 cursor-pointer hover:text-[#6EE35E]" />
              <User className="w-5 h-5 text-gray-400 cursor-pointer hover:text-[#6EE35E]" />
              <Menu className="w-5 h-5 text-gray-400 cursor-pointer md:hidden" />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <article className="bg-white rounded-lg shadow-sm p-8">
          {/* Article Header */}
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-4">
              <span className="bg-[#6EE35E] text-white px-3 py-1 rounded-full text-sm font-medium">
                Technology
              </span>
              <span className="text-gray-500 text-sm">2 hours ago</span>
            </div>
            <h1 className="text-3xl font-bold text-[#4C4C4C] mb-4 leading-tight">
              नेपाल इन्भेष्टमेन्ट बैंक र ई–सेवाबीच अनलाइन भुक्तानी सम्झौता, फोन पे सेवा पनि सुरु
            </h1>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <User size={16} />
                </div>
                <span>TechPana Reporter</span>
              </div>
              <span>•</span>
              <span>5 min read</span>
            </div>
          </div>

          {/* Article Image */}
          <div className="mb-8">
            <img
              src="https://images.unsplash.com/photo-**********-0cfed4f6a45d?auto=format&fit=crop&w=1200&h=600"
              alt="Digital Banking"
              className="w-full h-64 object-cover rounded-lg"
            />
            <p className="text-sm text-gray-500 mt-2 text-center">
              Digital banking services expanding in Nepal
            </p>
          </div>

          {/* Article Content */}
          <div className="prose prose-lg max-w-none">
            <p className="mb-6 text-gray-700 leading-relaxed">
              काठमाडौं, १५ पुष - ई–सेवा, फोन पे र नेपाल इन्भेष्टमेन्ट बैंकबीच अनलाइन भुक्तानी सम्बन्धी सम्झौता भएको छ। यस सम्झौताअन्तर्गत बैंकका ग्राहकहरूले ई–सेवामा रकम लोड गरी भुक्तानी सेवा उपयोग गर्न सक्नेछन्।
            </p>
            
            <p className="mb-6 text-gray-700 leading-relaxed">
              सम्झौतापछि ग्राहकहरूले मोबाइल रिचार्जदेखि लिएर बिजुली, खानेपानी, यातायात टिकट तथा विद्यालय शुल्क भुक्तानीसम्म विभिन्न सुविधाहरू ई–सेवाबाट सहजै गर्न सक्नेछन्। बैंकको इन्टरनेट बैंकिङ वा शाखाबाट पैसा ई–सेवामा लोड गर्न सकिने र ई–सेवामा भएको रकम बैंकमा पनि जम्मा गर्न सकिने व्यवस्था भएको छ।
            </p>

            <h2 className="text-2xl font-bold text-[#4C4C4C] mb-4 mt-8">
              फोन पे सेवाको विस्तार
            </h2>
            
            <p className="mb-6 text-gray-700 leading-relaxed">
              यसै अवसरमा नेपाल इन्भेष्टमेन्ट बैंकले फोन पे सेवा पनि सुरु गरेको छ। यो सेवाअन्तर्गत ग्राहकहरूले मोबाइल एप्लिकेसनमार्फत सजिलै भुक्तानी गर्न सक्नेछन्। बैंकका अनुसार यो सेवाले ग्राहकहरूको दैनिक भुक्तानी प्रक्रियालाई सहज बनाउनेछ।
            </p>

            <p className="mb-6 text-gray-700 leading-relaxed">
              ई–सेवामा हाल ५५ हजार भन्दा बढी क्युआर मर्चेन्टहरू आबद्ध छन् र क्यासब्याक तथा छुट सुविधासहित क्युआर कोडमार्फत भुक्तानी प्रवद्र्धन भैरहेको छ। यस सेवाले नेपालमा डिजिटल भुक्तानी प्रणालीलाई थप मजबुत बनाउने अपेक्षा गरिएको छ।
            </p>

            <div className="bg-gray-50 p-6 rounded-lg mb-6">
              <h3 className="text-lg font-semibold text-[#4C4C4C] mb-3">मुख्य विशेषताहरू:</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• इन्टरनेट बैंकिङ र शाखाबाट ई–सेवामा रकम लोड</li>
                <li>• विभिन्न बिल भुक्तानी सुविधा</li>
                <li>• क्युआर कोड भुक्तानी</li>
                <li>• क्यासब्याक र छुट सुविधा</li>
                <li>• मोबाइल एप्लिकेसन सुविधा</li>
              </ul>
            </div>

            <p className="mb-6 text-gray-700 leading-relaxed">
              बैंकका अधिकारीहरूका अनुसार यो सम्झौताले ग्राहकहरूलाई थप सुविधा प्रदान गर्नेछ र डिजिटल नेपालको अवधारणालाई साकार पार्न योगदान पुर्‍याउनेछ। आगामी दिनहरूमा यस्ता थप सेवाहरू विस्तार गर्ने योजना रहेको बैंकले जनाएको छ।
            </p>
          </div>

          {/* Article Footer */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-500">Share this article:</span>
                <div className="flex gap-2">
                  <button className="text-blue-600 hover:text-blue-700 text-sm">Facebook</button>
                  <button className="text-blue-400 hover:text-blue-500 text-sm">Twitter</button>
                  <button className="text-blue-800 hover:text-blue-900 text-sm">LinkedIn</button>
                </div>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>Published:</span>
                <time>December 30, 2024</time>
              </div>
            </div>
          </div>
        </article>

        {/* Related Articles Section */}
        <section className="mt-12">
          <h2 className="text-2xl font-bold text-[#4C4C4C] mb-6">Related Articles</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <article className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
              <img
                src="https://images.unsplash.com/photo-**********-824ae1b704d3?auto=format&fit=crop&w=400&h=200"
                alt="Mobile Banking"
                className="w-full h-32 object-cover rounded mb-4"
              />
              <h3 className="font-semibold text-[#4C4C4C] mb-2 hover:text-[#6EE35E] cursor-pointer">
                नेपालमा मोबाइल बैंकिङको बढ्दो चलन
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                डिजिटल भुक्तानी प्रणालीको विकासले नेपालमा मोबाइल बैंकिङको प्रयोग बढाएको छ...
              </p>
              <span className="text-xs text-gray-500">3 hours ago</span>
            </article>

            <article className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
              <img
                src="https://images.unsplash.com/photo-*************-c3d57bc86b40?auto=format&fit=crop&w=400&h=200"
                alt="Digital Economy"
                className="w-full h-32 object-cover rounded mb-4"
              />
              <h3 className="font-semibold text-[#4C4C4C] mb-2 hover:text-[#6EE35E] cursor-pointer">
                डिजिटल अर्थतन्त्रमा नेपालको प्रगति
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                सरकारी र निजी क्षेत्रको सहयोगमा नेपालको डिजिटल अर्थतन्त्र बलियो बन्दै गएको छ...
              </p>
              <span className="text-xs text-gray-500">5 hours ago</span>
            </article>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-[#4C4C4C] text-white mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-[#6EE35E] rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">TP</span>
                </div>
                <span className="text-xl font-bold">TechPana</span>
              </div>
              <p className="text-gray-300 text-sm">
                Nepal's leading technology and business news platform providing accurate and timely information.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Categories</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li><a href="#" className="hover:text-[#6EE35E]">Technology</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Business</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Economy</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Sports</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li><a href="#" className="hover:text-[#6EE35E]">About Us</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Contact</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Terms of Service</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Follow Us</h4>
              <ul className="space-y-2 text-sm text-gray-300">
                <li><a href="#" className="hover:text-[#6EE35E]">Facebook</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Twitter</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">LinkedIn</a></li>
                <li><a href="#" className="hover:text-[#6EE35E]">Instagram</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-600 mt-8 pt-8 text-center text-sm text-gray-300">
            <p>&copy; 2024 TechPana. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default NewsLayout;
