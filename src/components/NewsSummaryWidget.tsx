
import React from 'react';

interface NewsSummaryWidgetProps {
  summary: string;
  postId: string;
  isExpanded: boolean;
  onToggle: () => void;
}

const NewsSummaryWidget: React.FC<NewsSummaryWidgetProps> = ({
  summary,
  postId,
  isExpanded,
  onToggle
}) => {


  return (
    <div className="space-y-6">

      {/* Summary Content */}
      <div className="relative">
        <div className="bg-gradient-to-br from-gray-50 to-gray-100/50 rounded-2xl p-6 border border-gray-200/50 shadow-sm">
          <div className="prose prose-lg max-w-none">
            {summary.split('\n').map((line, index) => {
              const trimmedLine = line.trim();
              if (!trimmedLine) return null;

              return (
                <div
                  key={index}
                  className="mb-4 last:mb-0 slide-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-[#6EE35E] rounded-full mt-3"></div>
                    <p className="text-gray-700 leading-relaxed text-base font-medium">
                      {trimmedLine.replace(/^•\s*/, '')}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-[#6EE35E]/20 rounded-full"></div>
        <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-[#6EE35E]/10 rounded-full"></div>
      </div>


    </div>
  );
};

export default NewsSummaryWidget;
